{"rustc": 13226066032359371072, "features": "[\"__tls\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"serde_json\", \"stream\", \"tokio-native-tls\", \"tokio-util\", \"wasm-streams\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 16503403049695105087, "path": 6994241218797547596, "deps": [[40386456601120721, "percent_encoding", false, 17915051706241695583], [95042085696191081, "ipnet", false, 8643651624952566002], [264090853244900308, "sync_wrapper", false, 455633222614494139], [784494742817713399, "tower_service", false, 9957712316769032164], [1288403060204016458, "tokio_util", false, 1080212329476317053], [1906322745568073236, "pin_project_lite", false, 2075238954002039846], [2779053297469913730, "cookie_crate", false, 15448342763023535179], [3150220818285335163, "url", false, 4160233397614059452], [3722963349756955755, "once_cell", false, 15844034523397329668], [4405182208873388884, "http", false, 827515850289268980], [5986029879202738730, "log", false, 18014251309897682753], [7414427314941361239, "hyper", false, 14080373672804163322], [7620660491849607393, "futures_core", false, 14367975791664197490], [8915503303801890683, "http_body", false, 12876016119343823676], [9689903380558560274, "serde", false, 2414983301989832966], [10229185211513642314, "mime", false, 15578589532751084311], [10629569228670356391, "futures_util", false, 6333996886966486451], [12186126227181294540, "tokio_native_tls", false, 13039691884678295897], [12367227501898450486, "hyper_tls", false, 957796664974934728], [12393800526703971956, "tokio", false, 2364074762987337299], [13763625454224483636, "h2", false, 1102112474252151750], [14564311161534545801, "encoding_rs", false, 6839540289844771837], [15367738274754116744, "serde_json", false, 16839230193871291908], [16066129441945555748, "bytes", false, 16094739066178968150], [16311359161338405624, "rustls_pemfile", false, 704072778574897033], [16542808166767769916, "serde_urlencoded", false, 12006031710729317820], [16785601910559813697, "native_tls_crate", false, 18353590953646091267], [17973378407174338648, "cookie_store", false, 8174791308199821381], [18066890886671768183, "base64", false, 7327579160994683670]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/reqwest-53ea883f5e3cb256/dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}