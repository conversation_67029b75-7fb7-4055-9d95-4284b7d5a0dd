{"rustc": 13226066032359371072, "features": "[\"color\", \"default\", \"derive\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 2781995403500503566, "path": 2046688006128067571, "deps": [[1457576002496728321, "clap_derive", false, 15983197262863172844], [7361794428713524931, "clap_builder", false, 9526481484653479334]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/clap-c2deee40da973bf3/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}