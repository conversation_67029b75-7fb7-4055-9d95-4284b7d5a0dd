{"rustc": 13226066032359371072, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 2781995403500503566, "path": 14711087538214754121, "deps": [[5820056977320921005, "anstream", false, 11393532125051589479], [9394696648929125047, "anstyle", false, 5136714305649250286], [11166530783118767604, "strsim", false, 9271446983612623310], [11649982696571033535, "clap_lex", false, 3476014573276874039]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/clap_builder-37740f6ea067cf43/dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}