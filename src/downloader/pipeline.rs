/*!
 * 流水线处理实现
 */

use anyhow::Result;
use std::sync::Arc;
use futures::stream::{self, StreamExt};
use tracing::{info, warn, error};

use crate::config::Config;
use crate::api::PixivClient;
use crate::core::{ArtworkInfo, UserInfo, DownloadResult, DownloadError, DownloadErrorType};
use crate::storage::StorageManager;

/// 下载流水线
pub struct DownloadPipeline {
    /// 配置
    config: Arc<Config>,
    /// API 客户端
    client: Arc<PixivClient>,
    /// 用户信息
    user_info: UserInfo,
    /// 存储管理器
    storage_manager: StorageManager,
}

impl DownloadPipeline {
    /// 创建新的下载流水线
    pub fn new(
        config: Arc<Config>,
        client: Arc<PixivClient>,
        user_info: UserInfo,
    ) -> Self {
        let storage_manager = StorageManager::new((*config).clone());

        Self {
            config,
            client,
            user_info,
            storage_manager,
        }
    }

    /// 处理作品列表
    pub async fn process(&self, artworks: Vec<ArtworkInfo>) -> Result<DownloadResult> {
        info!("开始处理 {} 个作品", artworks.len());

        let mut successful_downloads = 0;
        let mut failed_downloads = 0;
        let mut skipped_downloads = 0;
        let mut errors = Vec::new();

        // 使用流水线并发处理
        let results = stream::iter(artworks)
            .map(|artwork| self.process_single_artwork(artwork))
            .buffer_unordered(self.config.download.concurrency)
            .collect::<Vec<_>>()
            .await;

        // 统计结果
        for (index, result) in results.into_iter().enumerate() {
            match result {
                Ok(ProcessResult::Success) => successful_downloads += 1,
                Ok(ProcessResult::Skipped) => skipped_downloads += 1,
                Err(e) => {
                    failed_downloads += 1;
                    let pid = artworks.get(index).map(|a| a.pid).unwrap_or(0);
                    let error = DownloadError {
                        pid,
                        error: e.to_string(),
                        error_type: self.classify_error(&e),
                    };
                    errors.push(error);
                }
            }
        }

        info!(
            "处理完成: 成功 {}, 失败 {}, 跳过 {}",
            successful_downloads, failed_downloads, skipped_downloads
        );

        Ok(DownloadResult {
            successful_downloads,
            failed_downloads,
            skipped_downloads,
            errors,
        })
    }

    /// 处理单个作品
    async fn process_single_artwork(&self, artwork: ArtworkInfo) -> Result<ProcessResult> {
        info!("正在处理作品: {} - {}", artwork.pid, artwork.title);

        // 添加随机延迟
        let delay = rand::random::<u64>() %
            (self.config.download.delay_range[1] - self.config.download.delay_range[0] + 1) +
            self.config.download.delay_range[0];
        tokio::time::sleep(tokio::time::Duration::from_secs(delay)).await;

        match artwork.artwork_type {
            crate::core::ArtworkType::Illust | crate::core::ArtworkType::Manga => {
                self.download_images(&artwork).await
            }
            crate::core::ArtworkType::Novel => {
                self.download_novel(&artwork).await
            }
            crate::core::ArtworkType::Unknown => {
                warn!("跳过未知类型作品: {}", artwork.pid);
                Ok(ProcessResult::Skipped)
            }
        }
    }

    /// 下载图片作品
    async fn download_images(&self, artwork: &ArtworkInfo) -> Result<ProcessResult> {
        if artwork.image_urls.is_empty() {
            warn!("作品 {} 没有图片URL", artwork.pid);
            return Ok(ProcessResult::Skipped);
        }

        for (index, url) in artwork.image_urls.iter().enumerate() {
            let page_index = if artwork.page_count > 1 { Some(index as u32) } else { None };

            // 解析文件路径
            let file_path = self.storage_manager.resolve_file_path(
                artwork,
                &self.user_info,
                page_index,
            )?;

            // 检查文件冲突
            let final_path = match self.storage_manager.handle_file_conflict(&file_path).await {
                Ok(path) => path,
                Err(_) => {
                    info!("跳过已存在的文件: {}", file_path.display());
                    continue;
                }
            };

            // 下载文件
            match self.download_file(url, &final_path).await {
                Ok(_) => {
                    info!("成功下载: {}", final_path.display());

                    // 生成元数据文件
                    if let Some(parent) = final_path.parent() {
                        let metadata_path = parent.join(format!("{}_metadata.txt", artwork.pid));
                        if let Err(e) = self.storage_manager.save_metadata(artwork, &self.user_info, &metadata_path).await {
                            warn!("保存元数据失败: {}", e);
                        }
                    }
                }
                Err(e) => {
                    error!("下载失败 {}: {}", url, e);
                    return Err(e);
                }
            }
        }

        Ok(ProcessResult::Success)
    }

    /// 下载小说作品
    async fn download_novel(&self, artwork: &ArtworkInfo) -> Result<ProcessResult> {
        info!("正在下载小说: {} - {}", artwork.pid, artwork.title);

        // 获取小说详细内容
        let novel_content = match self.fetch_novel_content(artwork.pid).await {
            Ok(content) => content,
            Err(e) => {
                warn!("获取小说内容失败: {}", e);
                return Ok(ProcessResult::Skipped);
            }
        };

        // 创建包含内容的作品信息
        let mut novel_artwork = artwork.clone();
        novel_artwork.novel_content = Some(novel_content);

        // 解析文件路径
        let file_path = self.storage_manager.resolve_file_path(
            &novel_artwork,
            &self.user_info,
            None,
        )?;

        // 检查文件冲突
        let final_path = match self.storage_manager.handle_file_conflict(&file_path).await {
            Ok(path) => path,
            Err(_) => {
                info!("跳过已存在的小说文件: {}", file_path.display());
                return Ok(ProcessResult::Skipped);
            }
        };

        // 生成小说文件内容（包含元数据和正文）
        let novel_file_content = self.generate_novel_file_content(&novel_artwork)?;

        // 保存小说文件
        match self.storage_manager.save_file(&final_path, novel_file_content.as_bytes()).await {
            Ok(_) => {
                info!("成功下载小说: {}", final_path.display());
                Ok(ProcessResult::Success)
            }
            Err(e) => {
                error!("保存小说文件失败: {}", e);
                Err(e)
            }
        }
    }

    /// 获取小说内容
    async fn fetch_novel_content(&self, pid: u64) -> Result<String> {
        let url = format!("https://www.pixiv.net/ajax/novel/{}", pid);

        let response = self.client.download_file(&url).await?;
        let json: serde_json::Value = response.json().await?;

        let body = json.get("body")
            .ok_or_else(|| anyhow::anyhow!("响应中缺少 body 字段"))?;

        let content = body.get("content")
            .and_then(|v| v.as_str())
            .ok_or_else(|| anyhow::anyhow!("无法获取小说内容"))?;

        Ok(content.to_string())
    }

    /// 生成小说文件内容
    fn generate_novel_file_content(&self, artwork: &ArtworkInfo) -> Result<String> {
        use crate::storage::generate_metadata_content;

        let mut content = String::new();

        // 添加元数据
        let metadata = generate_metadata_content(artwork, &self.user_info)?;
        content.push_str(&metadata);

        // 添加分隔符
        content.push_str("\n--- 正文内容 ---\n\n");

        // 添加小说正文
        if let Some(novel_content) = &artwork.novel_content {
            content.push_str(novel_content);
        }

        Ok(content)
    }

    /// 下载单个文件（带重试机制）
    async fn download_file(&self, url: &str, file_path: &std::path::PathBuf) -> Result<()> {
        let mut last_error = None;

        for attempt in 1..=self.config.download.retry_count {
            match self.try_download_file(url).await {
                Ok(bytes) => {
                    self.storage_manager.save_file(file_path, &bytes).await?;
                    return Ok(());
                }
                Err(e) => {
                    last_error = Some(e);
                    if attempt < self.config.download.retry_count {
                        warn!("下载失败，第 {} 次重试: {}", attempt, url);
                        // 重试前等待一段时间
                        tokio::time::sleep(tokio::time::Duration::from_secs(attempt as u64)).await;
                    }
                }
            }
        }

        Err(last_error.unwrap_or_else(|| anyhow::anyhow!("下载失败")))
    }

    /// 尝试下载文件
    async fn try_download_file(&self, url: &str) -> Result<bytes::Bytes> {
        let response = self.client.download_file(url).await?;
        let bytes = response.bytes().await?;
        Ok(bytes)
    }

    /// 分类错误类型
    fn classify_error(&self, error: &anyhow::Error) -> DownloadErrorType {
        let error_str = error.to_string().to_lowercase();

        if error_str.contains("network") || error_str.contains("timeout") || error_str.contains("connection") {
            DownloadErrorType::Network
        } else if error_str.contains("permission") || error_str.contains("access") || error_str.contains("io") {
            DownloadErrorType::FileSystem
        } else if error_str.contains("api") || error_str.contains("http") {
            DownloadErrorType::Api
        } else if error_str.contains("auth") || error_str.contains("unauthorized") {
            DownloadErrorType::Authentication
        } else {
            DownloadErrorType::Other
        }
    }
}

/// 处理结果
enum ProcessResult {
    Success,
    Skipped,
}
