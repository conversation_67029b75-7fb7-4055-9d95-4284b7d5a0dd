/*!
 * 下载模块
 */

pub mod concurrent;
pub mod pipeline;
pub mod progress;

use anyhow::Result;
use std::sync::Arc;

pub use concurrent::*;
pub use pipeline::*;
pub use progress::*;

use crate::config::Config;
use crate::api::PixivClient;
use crate::core::{DownloadTask, DownloadResult};

/// 并发下载器
pub struct ConcurrentDownloader {
    /// 配置
    config: Arc<Config>,
    /// API 客户端
    client: Arc<PixivClient>,
}

impl ConcurrentDownloader {
    /// 创建新的并发下载器
    pub fn new(config: Arc<Config>, client: Arc<PixivClient>) -> Self {
        Self {
            config,
            client,
        }
    }
    
    /// 执行下载任务
    pub async fn execute(&self, task: DownloadTask) -> Result<DownloadResult> {
        use tracing::{info, warn};
        
        info!("开始执行下载任务，用户 ID: {}", task.uid);
        
        // 获取用户信息
        let user_info = self.client.get_user_info(task.uid).await?;
        info!("用户信息: {} ({})", user_info.display_name, user_info.username);
        
        // 获取用户作品列表
        info!("正在获取用户作品列表...");
        let mut artworks = self.client.get_user_artworks(task.uid).await?;
        info!("获取到 {} 个作品", artworks.len());
        
        // 应用过滤器
        if let Some(tag_filter) = &task.tag_filter {
            artworks.retain(|artwork| tag_filter.matches(&artwork.tags));
            info!("标签过滤后剩余 {} 个作品", artworks.len());
        }

        // 应用作品类型过滤器
        artworks.retain(|artwork| task.artwork_type_filter.matches(&artwork.artwork_type));
        info!("类型过滤后剩余 {} 个作品", artworks.len());
        
        if artworks.is_empty() {
            warn!("没有符合条件的作品需要下载");
            return Ok(DownloadResult {
                successful_downloads: 0,
                failed_downloads: 0,
                skipped_downloads: 0,
                errors: Vec::new(),
            });
        }
        
        // 创建流水线处理器
        let pipeline = DownloadPipeline::new(
            self.config.clone(),
            self.client.clone(),
            user_info,
        );
        
        // 执行流水线下载
        pipeline.process(artworks).await
    }
}
