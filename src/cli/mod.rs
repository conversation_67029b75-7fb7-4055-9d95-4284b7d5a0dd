/*!
 * 命令行界面模块
 */

pub mod args;
pub mod interactive;
pub mod ui_components;

use anyhow::Result;
use clap::Parser;
use std::path::PathBuf;

pub use args::*;
pub use interactive::*;
pub use ui_components::*;

use crate::core::PixivDownloader;

/// 命令行参数结构
#[derive(Parser, Debug)]
#[command(
    name = "PixivTagDownloader",
    version = "0.1.0",
    author = "Mannix Sun <<EMAIL>>",
    about = "Pixiv 标签下载器 - 根据标签下载 Pixiv 用户作品"
)]
pub struct Args {
    /// Pixiv 用户 ID
    #[arg(short = 'u', long = "uid", help = "指定 Pixiv 用户 ID")]
    pub uid: Option<u64>,
    
    /// 要下载的标签（多个标签用逗号分隔）
    #[arg(short = 't', long = "tags", help = "指定要下载的标签，多个标签用逗号分隔")]
    pub tags: Option<String>,
    
    /// 标签过滤逻辑
    #[arg(
        short = 'l', 
        long = "logic", 
        help = "指定标签过滤逻辑",
        value_enum,
        default_value = "or"
    )]
    pub logic: TagLogic,
    
    /// 输出根目录
    #[arg(long = "output-dir", help = "指定输出根目录")]
    pub output_dir: Option<PathBuf>,
    
    /// 配置文件路径
    #[arg(long = "config", help = "指定自定义配置文件路径")]
    pub config: Option<PathBuf>,
    
    /// 并发下载任务数
    #[arg(long = "concurrency", help = "指定并发下载任务数")]
    #[arg(long = "threads", help = "指定并发下载任务数（concurrency 的别名）")]
    pub concurrency: Option<usize>,
    
    /// 随机延迟范围
    #[arg(long = "delay", help = "指定随机延迟范围（例如 1-3）")]
    pub delay: Option<String>,
    
    /// 文件冲突处理策略
    #[arg(long = "conflict", help = "文件冲突处理策略", value_enum)]
    pub conflict: Option<ConflictStrategy>,

    /// 跳过已存在文件
    #[arg(long = "skip-existing", help = "跳过已存在的文件")]
    pub skip_existing: bool,

    /// 覆盖已存在文件
    #[arg(long = "overwrite-existing", help = "覆盖已存在的文件")]
    pub overwrite_existing: bool,

    /// 重命名新文件
    #[arg(long = "rename-existing", help = "重命名新文件")]
    pub rename_existing: bool,
    
    /// 日志级别
    #[arg(long = "log-level", help = "设置日志级别")]
    pub log_level: Option<String>,
    
    /// 下载所有作品（不进行标签筛选）
    #[arg(long = "all", help = "下载用户所有作品，不进行标签筛选")]
    pub all: bool,
    
    /// 作品类型
    #[arg(
        long = "type", 
        help = "指定要下载的作品类型",
        value_enum,
        default_value = "all"
    )]
    pub artwork_type: ArtworkTypeFilter,
}

/// 标签过滤逻辑
#[derive(Debug, Clone, clap::ValueEnum)]
pub enum TagLogic {
    /// 且（AND）- 作品需同时包含所有选定标签
    #[value(name = "and")]
    And,
    /// 或（OR）- 作品只需包含任意一个选定标签
    #[value(name = "or")]
    Or,
    /// 非（NOT）- 排除包含指定标签的作品
    #[value(name = "not")]
    Not,
}

/// 文件冲突处理策略
#[derive(Debug, Clone, clap::ValueEnum)]
pub enum ConflictStrategy {
    /// 跳过已存在文件
    #[value(name = "skip")]
    Skip,
    /// 覆盖已存在文件
    #[value(name = "overwrite")]
    Overwrite,
    /// 重命名新文件
    #[value(name = "rename")]
    Rename,
}

/// 作品类型过滤器
#[derive(Debug, Clone, clap::ValueEnum)]
pub enum ArtworkTypeFilter {
    /// 插画
    #[value(name = "illust")]
    Illust,
    /// 漫画
    #[value(name = "manga")]
    Manga,
    /// 小说
    #[value(name = "novel")]
    Novel,
    /// 全部
    #[value(name = "all")]
    All,
}

impl Args {
    /// 检查是否为交互模式
    pub fn is_interactive_mode(&self) -> bool {
        // 如果没有指定 UID，则进入交互模式
        self.uid.is_none()
    }
}

/// 交互式模式入口
pub async fn interactive_mode(downloader: PixivDownloader) -> Result<()> {
    run_interactive_mode(downloader).await
}
