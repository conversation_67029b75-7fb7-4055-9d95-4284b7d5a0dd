/*!
 * 基本使用示例
 * 
 * 这个示例展示了如何使用 Pixiv 标签下载器库的基本功能
 */

use pixiv_tag_downloader::{
    Config, PixivDownloader, DownloadTask, 
    TagFilter, TagFilterLogic, ArtworkTypeFilter
};
use std::path::PathBuf;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();
    
    println!("Pixiv 标签下载器 - 基本使用示例");
    println!("版本: {}", pixiv_tag_downloader::version());
    
    // 加载配置
    let config_path = PathBuf::from("config.yaml");
    
    // 检查配置文件是否存在
    if !config_path.exists() {
        println!("配置文件不存在，正在创建示例配置...");
        let config = Config::load(&config_path).await;
        match config {
            Ok(_) => println!("配置加载成功"),
            Err(e) => {
                println!("请编辑配置文件后重新运行: {}", e);
                return Ok(());
            }
        }
    }
    
    // 加载配置
    let config = match Config::load(&config_path).await {
        Ok(config) => config,
        Err(e) => {
            println!("配置加载失败: {}", e);
            return Ok(());
        }
    };
    
    // 验证配置
    if let Err(e) = config.validate() {
        println!("配置验证失败: {}", e);
        return Ok(());
    }
    
    // 创建下载器
    let downloader = match PixivDownloader::new(config).await {
        Ok(downloader) => downloader,
        Err(e) => {
            println!("下载器创建失败: {}", e);
            return Ok(());
        }
    };
    
    // 示例：获取用户信息
    let uid = 123456; // 替换为实际的用户 ID
    println!("正在获取用户 {} 的信息...", uid);
    
    match downloader.get_user_info(uid).await {
        Ok(user_info) => {
            println!("用户信息:");
            println!("  UID: {}", user_info.uid);
            println!("  用户名: {}", user_info.username);
            println!("  显示名称: {}", user_info.display_name);
        }
        Err(e) => {
            println!("获取用户信息失败: {}", e);
            return Ok(());
        }
    }
    
    // 示例：获取用户作品列表
    println!("正在获取用户作品列表...");
    match downloader.get_user_artworks(uid).await {
        Ok(artworks) => {
            println!("找到 {} 个作品", artworks.len());
            
            // 显示前几个作品的信息
            for (i, artwork) in artworks.iter().take(5).enumerate() {
                println!("  作品 {}: {} - {} ({})", 
                    i + 1, artwork.pid, artwork.title, artwork.artwork_type.to_chinese());
            }
            
            // 提取标签
            let tags = downloader.extract_tags(&artworks).await;
            println!("找到 {} 个不同的标签", tags.len());
            if !tags.is_empty() {
                println!("前10个标签: {:?}", &tags[..tags.len().min(10)]);
            }
        }
        Err(e) => {
            println!("获取作品列表失败: {}", e);
            return Ok(());
        }
    }
    
    // 示例：创建下载任务（仅演示，不实际下载）
    println!("\n创建下载任务示例:");
    
    // 创建标签过滤器
    let tag_filter = TagFilter::new(
        vec!["原创".to_string(), "插画".to_string()],
        TagFilterLogic::Or
    );
    
    let download_task = DownloadTask {
        uid,
        tag_filter: Some(tag_filter),
        artwork_type_filters: vec![ArtworkTypeFilter::Illust],
        output_dir_override: Some("./example_output".to_string()),
    };
    
    println!("下载任务已创建:");
    println!("  用户 UID: {}", download_task.uid);
    println!("  作品类型: 插画");
    println!("  标签过滤: 包含 '原创' 或 '插画' 标签的作品");
    
    // 注意：这里不实际执行下载，因为需要有效的认证信息
    println!("\n注意: 要实际下载作品，请:");
    println!("1. 在配置文件中填入有效的 Pixiv 认证信息");
    println!("2. 调用 downloader.download(download_task).await");
    
    println!("\n示例完成！");
    Ok(())
}
